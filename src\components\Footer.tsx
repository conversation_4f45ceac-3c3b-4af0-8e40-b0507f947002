
import { Link } from 'react-router-dom';
import { Mail, Phone } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-blue-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <img 
                src="/lovable-uploads/b211efc9-6b32-47ba-9b28-ccec6a482c05.png" 
                alt="FundIt Logo" 
                className="h-12 w-auto filter brightness-0 invert"
              />
              <div>
                <p className="text-blue-200 text-sm">Innovating Financial Solutions for Zambia's Growth</p>
              </div>
            </div>
            <p className="text-blue-100 mb-4">
              Transforming how short to medium-term financing is accessed by individuals and 
              small to medium-sized enterprises (SMEs) in Zambia.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li><Link to="/" className="text-blue-200 hover:text-white transition-colors">Home</Link></li>
              <li><Link to="/about" className="text-blue-200 hover:text-white transition-colors">About Us</Link></li>
              <li><Link to="/services" className="text-blue-200 hover:text-white transition-colors">Services</Link></li>
              <li><Link to="/application" className="text-blue-200 hover:text-white transition-colors">Apply Now</Link></li>
              <li><Link to="/contact" className="text-blue-200 hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Phone size={16} />
                <span className="text-blue-200">+260 962 000 978</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail size={16} />
                <span className="text-blue-200"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-blue-800 mt-8 pt-8 text-center">
          <p className="text-blue-200">
            © 2024 FundIt Capital Solutions Limited. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
