
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const About = () => {
  const executives = [
    {
      name: "<PERSON>",
      position: "Chief Executive Officer",
      experience: "Over 10 years of experience in Zambia's financial sector",
      credentials: "Certified Risk Analyst (CRA), Certified Governance, Risk Management, and Compliance Specialist, MBA from University of Zambia",
      email: "<EMAIL>",
      phone: "+260 962 000 978"
    },
    {
      name: "<PERSON>",
      position: "Chief Operations Officer",
      experience: "More than 7 years of experience in credit and relationship management",
      credentials: "Bachelor's degree in Banking and Finance from Copperbelt University, pursuing Master's degree in Finance at UNICAF University",
      email: "<EMAIL>",
      phone: "+260 967 998 648"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      position: "Chief Strategy Officer",
      experience: "Over 11 years of experience as an economist and financial expert",
      credentials: "MCom in Development Finance from University of Cape Town, significant contributions to Zambia's financial and energy sectors",
      email: "<EMAIL>",
      phone: "+260 967 880 912"
    }
  ];

  const values = [
    {
      title: "Innovation",
      description: "We continuously develop efficient financial solutions tailored to our clients' needs."
    },
    {
      title: "Customer Focus",
      description: "We ensure outstanding service and a seamless borrowing experience."
    },
    {
      title: "Integrity",
      description: "We uphold transparency, accountability, and ethical practices in all our operations."
    },
    {
      title: "Responsibility",
      description: "We prioritize responsible lending to create value for clients and contribute positively to communities."
    },
    {
      title: "Teamwork",
      description: "We cultivate a supportive and collaborative work environment to achieve shared goals."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">About FundIt Capital Solutions</h1>
            <p className="text-xl text-blue-100">
              Transforming financial services in Zambia since 2024, dedicated to innovative 
              solutions that empower individuals and SMEs to achieve their financial goals.
            </p>
          </div>
        </div>
      </section>

      {/* Company Overview */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Our Story</h2>
            <div className="prose prose-lg max-w-none text-gray-600">
              <p className="mb-6">
                FundIt Capital Solutions Limited is a Zambian financial services provider established in 2024, 
                dedicated to transforming how short to medium-term financing is accessed by individuals and 
                small to medium-sized enterprises (SMEs). We are driven by the mission to address gaps in 
                traditional financial services and create innovative, efficient solutions that meet the 
                evolving needs of our clients.
              </p>
              <p className="mb-6">
                With a customer-centric approach, FundIt offers fast turnaround times, minimal documentation, 
                and precise credit appraisal to provide a seamless borrowing experience. Our team is young, 
                vibrant, and highly skilled, with significant expertise in Zambia's financial sector.
              </p>
              <p>
                We are committed to fostering a culture of innovation and excellence to ensure our clients 
                receive tailored financial products and services. By rethinking conventional methods of loan 
                appraisal and leveraging flexible tools, FundIt empowers SMEs and individuals to overcome 
                cash flow challenges and achieve their financial goals.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Vision & Mission */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-blue-600 text-white">
                <CardTitle className="text-2xl">Our Vision</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-gray-700">
                  To be the preferred financial solutions provider for personal credit and SME financing 
                  in Zambia, recognized for our innovative products, exceptional customer service, and 
                  impactful contributions to societal development.
                </p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-green-600 text-white">
                <CardTitle className="text-2xl">Our Mission</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-gray-700">
                  To deliver innovative and accessible financial solutions that support individuals and SMEs, 
                  enabling them to achieve their aspirations while fostering sustainable economic growth in Zambia.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Core Values */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Our Core Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {values.map((value, index) => (
              <Card key={index} className="border-l-4 border-l-blue-600 shadow-md hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-xl text-blue-900">{value.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Executive Team */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Our Executive Team</h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {executives.map((exec, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader className="text-center">
                  <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-3xl font-bold text-blue-600">
                      {exec.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <CardTitle className="text-xl text-gray-900">{exec.name}</CardTitle>
                  <p className="text-blue-600 font-semibold">{exec.position}</p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600 text-sm">{exec.experience}</p>
                  <p className="text-gray-600 text-sm">{exec.credentials}</p>
                  <div className="pt-4 border-t space-y-1">
                    <p className="text-sm text-gray-500">Email: {exec.email}</p>
                    <p className="text-sm text-gray-500">Phone: {exec.phone}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
