
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Users, Briefcase, FileText } from 'lucide-react';

const Services = () => {
  const businessLoans = [
    {
      title: "Invoice Discounting",
      description: "Designed to provide immediate working capital by allowing businesses to draw funds against their sales invoices. This solution helps improve cash flow and enables businesses to meet their operational needs seamlessly.",
      features: ["Immediate working capital", "Based on sales invoices", "Improves cash flow", "Quick processing"],
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      title: "Order Financing",
      description: "A short-term commercial financing option that offers capital to pay suppliers upfront for verified purchase orders or contracts. This facility ensures SMEs can fulfill orders without depleting their cash reserves.",
      features: ["Pay suppliers upfront", "Based on verified orders", "Preserve cash reserves", "Short-term facility"],
      image: "https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      title: "Term Loans",
      description: "Suitable for SMEs with long-term contracts, offering flexible repayment tenures of up to 12 months to support sustainable business growth.",
      features: ["Up to 12 months tenure", "For long-term contracts", "Sustainable growth", "Flexible repayment"],
      image: "https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-4.0.3&ixid=M3wxMJA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    }
  ];

  const personalLoans = [
    {
      title: "Payroll-Deducted Loans",
      description: "A short- to medium-term credit facility available to employees of companies that have signed a Memorandum of Understanding (MOU) with FundIt. Funds are disbursed within 24 hours of completing the application process.",
      benefits: ["No collateral required", "24-hour disbursement", "Payroll deduction", "Early repayment options", "Salary-based loan sizes"],
      image: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-4.0.3&ixid=M3wxMJA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    },
    {
      title: "Instant Salary Advances",
      description: "A short-term credit facility designed for individuals in formal employment, providing funds within 24 hours. This solution is ideal for addressing urgent financial needs with minimal documentation requirements.",
      requirements: ["Payslip", "KYC documents (ID and proof of address)", "Confirmation letter from employer"],
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-4.0.3&ixid=M3wxMJA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
    }
  ];

  const whyChoose = [
    {
      icon: <CreditCard className="text-blue-600" size={32} />,
      title: "Speed and Efficiency",
      description: "Our loans are processed quickly, with disbursements typically completed within 24 hours of application."
    },
    {
      icon: <Users className="text-green-600" size={32} />,
      title: "Flexibility",
      description: "Our tailored loan products designed to suit unique business and personal needs."
    },
    {
      icon: <Briefcase className="text-purple-600" size={32} />,
      title: "No Collateral Requirements",
      description: "Many of our loans do not require collateral, making financing accessible to more clients."
    },
    {
      icon: <FileText className="text-orange-600" size={32} />,
      title: "Responsible Lending",
      description: "Our affordability criteria ensure borrowers remain financially stable even after taking a loan."
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-900 to-blue-700 text-white py-16 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-30"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: "url('https://images.unsplash.com/photo-1487958449943-2429e8be8625?ixlib=rb-4.0.3&ixid=M3wxMJA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80')",
            opacity: 0.3
          }}
        ></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Financial Services</h1>
            <p className="text-xl text-blue-100">
              Comprehensive financial products tailored to meet the needs of individuals and 
              businesses across Zambia. From quick personal loans to business financing solutions.
            </p>
          </div>
        </div>
      </section>

      {/* Business Loans Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Business Loans</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive financing solutions designed to fuel business growth and improve cash flow.
            </p>
            <div className="mt-4">
              <Badge variant="outline" className="text-lg px-4 py-2 border-blue-600 text-blue-600">
                Amounts: K25,000 - K1,000,000
              </Badge>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {businessLoans.map((loan, index) => (
              <Card key={index} className="border-l-4 border-l-blue-600 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={loan.image} 
                    alt={loan.title}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardHeader>
                  <CardTitle className="text-xl text-blue-900">{loan.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600">{loan.description}</p>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                    <ul className="space-y-1">
                      {loan.features.map((feature, idx) => (
                        <li key={idx} className="text-sm text-gray-600 flex items-center">
                          <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Personal Loans Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Personal Loans</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Quick and accessible personal financing solutions for individuals in formal employment.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {personalLoans.map((loan, index) => (
              <Card key={index} className="border-l-4 border-l-green-600 shadow-lg hover:shadow-xl transition-shadow overflow-hidden">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={loan.image} 
                    alt={loan.title}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <CardHeader>
                  <CardTitle className="text-xl text-green-900">{loan.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-600">{loan.description}</p>
                  
                  {loan.benefits && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Key Benefits:</h4>
                      <ul className="space-y-1">
                        {loan.benefits.map((benefit, idx) => (
                          <li key={idx} className="text-sm text-gray-600 flex items-center">
                            <span className="w-2 h-2 bg-green-600 rounded-full mr-2"></span>
                            {benefit}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {loan.requirements && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Eligibility Requirements:</h4>
                      <ul className="space-y-1">
                        {loan.requirements.map((req, idx) => (
                          <li key={idx} className="text-sm text-gray-600 flex items-center">
                            <span className="w-2 h-2 bg-green-600 rounded-full mr-2"></span>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose FundIt */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Choose FundIt?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We stand out in the financial services industry with our commitment to innovation, 
              speed, and customer satisfaction.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {whyChoose.map((item, index) => (
              <Card key={index} className="text-center border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    {item.icon}
                  </div>
                  <CardTitle className="text-lg text-gray-900">{item.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{item.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section with Background Image */}
      <section 
        className="py-16 bg-blue-600 text-white relative overflow-hidden"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1551038247-3d9af20df552?ixlib=rb-4.0.3&ixid=M3wxMJA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2000&q=80')",
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="absolute inset-0 bg-blue-900 bg-opacity-80"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Apply?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Choose the loan product that best fits your needs and start your application today. 
            Get funded within 24 hours with minimal documentation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/application">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold">
                Start Application
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
                Speak with an Expert
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Services;
