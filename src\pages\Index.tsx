import { useState } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { ArrowDown, Users, CreditCard, DollarSign, CheckCircle, Clock, Shield } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Index = () => {
  const { toast } = useToast();
  const [inquiryForm, setInquiryForm] = useState({
    name: '',
    email: '',
    phone: '',
    loanType: '',
    amount: '',
    message: ''
  });

  const handleInquirySubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Inquiry Submitted",
      description: "Thank you for your interest. We'll contact you within 24 hours.",
    });
    setInquiryForm({
      name: '',
      email: '',
      phone: '',
      loanType: '',
      amount: '',
      message: ''
    });
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-blue-600 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-80 h-80 bg-white rounded-full translate-x-1/3 translate-y-1/3"></div>
        </div>
        
        <div className="relative container mx-auto px-4 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 animate-fade-in">
              {/* Logo with Text Combination */}
              <div className="flex items-center space-x-4 mb-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-white/20 rounded-full blur-xl"></div>
                  <img 
                    src="/lovable-uploads/b211efc9-6b32-47ba-9b28-ccec6a482c05.png" 
                    alt="FundIt Logo" 
                    className="relative h-16 w-auto filter drop-shadow-lg"
                  />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-yellow-400">FundIt Capital</h2>
                  <p className="text-blue-200 text-sm">Solutions Limited</p>
                </div>
              </div>

              <h1 className="text-4xl md:text-5xl font-bold leading-tight">
                Innovative Financial Solutions for 
                <span className="text-yellow-400"> Zambia's Growth</span>
              </h1>
              <p className="text-xl text-blue-100">
                Fast, flexible financing for individuals and SMEs. Get funded within 24 hours 
                with minimal documentation and no collateral required.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/application">
                  <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
                    Apply for Loan
                  </Button>
                </Link>
                <Link to="/about">
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-900 transition-all duration-300">
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>

            {/* Quick Inquiry Form with Image Background */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-3xl"></div>
              <div className="relative bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 transition-all duration-300 hover:bg-white/15">
                <h3 className="text-2xl font-semibold mb-4">Quick Inquiry</h3>
                <form onSubmit={handleInquirySubmit} className="space-y-4">
                  <Input
                    placeholder="Full Name"
                    value={inquiryForm.name}
                    onChange={(e) => setInquiryForm({...inquiryForm, name: e.target.value})}
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70 transition-all duration-300 focus:bg-white/30"
                    required
                  />
                  <Input
                    type="email"
                    placeholder="Email Address"
                    value={inquiryForm.email}
                    onChange={(e) => setInquiryForm({...inquiryForm, email: e.target.value})}
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70 transition-all duration-300 focus:bg-white/30"
                    required
                  />
                  <Input
                    placeholder="Phone Number"
                    value={inquiryForm.phone}
                    onChange={(e) => setInquiryForm({...inquiryForm, phone: e.target.value})}
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70 transition-all duration-300 focus:bg-white/30"
                    required
                  />
                  <select
                    value={inquiryForm.loanType}
                    onChange={(e) => setInquiryForm({...inquiryForm, loanType: e.target.value})}
                    className="w-full px-3 py-2 bg-white/20 border border-white/30 rounded-md text-white transition-all duration-300 focus:bg-white/30"
                    required
                  >
                    <option value="" className="text-black">Select Loan Type</option>
                    <option value="personal" className="text-black">Personal Loan</option>
                    <option value="business" className="text-black">Business Loan</option>
                    <option value="invoice" className="text-black">Invoice Discounting</option>
                    <option value="order" className="text-black">Order Financing</option>
                  </select>
                  <Input
                    placeholder="Loan Amount (ZMW)"
                    value={inquiryForm.amount}
                    onChange={(e) => setInquiryForm({...inquiryForm, amount: e.target.value})}
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70 transition-all duration-300 focus:bg-white/30"
                  />
                  <Textarea
                    placeholder="Additional Message"
                    value={inquiryForm.message}
                    onChange={(e) => setInquiryForm({...inquiryForm, message: e.target.value})}
                    className="bg-white/20 border-white/30 text-white placeholder:text-white/70 transition-all duration-300 focus:bg-white/30"
                    rows={3}
                  />
                  <Button type="submit" className="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-semibold transition-all duration-300 hover:scale-105">
                    Submit Inquiry
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <ArrowDown className="text-white/70" size={24} />
        </div>
      </section>

      {/* About Us Brief Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6 animate-fade-in">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                About FundIt Capital
              </h2>
              <p className="text-lg text-gray-700 leading-relaxed">
                FundIt Capital Solutions Limited is a leading financial services company in Zambia, 
                dedicated to empowering individuals and small-to-medium enterprises with innovative 
                financing solutions.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                Since our establishment, we have been committed to bridging the financial gap in 
                Zambia's economy by providing fast, flexible, and accessible lending services. 
                Our mission is to fuel growth and create opportunities for businesses and individuals 
                across the nation.
              </p>
              <div className="grid grid-cols-2 gap-6 mt-8">
                <div className="text-center">
                  <h3 className="text-3xl font-bold text-blue-600">1000+</h3>
                  <p className="text-gray-600">Happy Clients</p>
                </div>
                <div className="text-center">
                  <h3 className="text-3xl font-bold text-yellow-500">24hrs</h3>
                  <p className="text-gray-600">Quick Processing</p>
                </div>
              </div>
              <Link to="/about">
                <Button className="bg-blue-600 hover:bg-blue-700 transition-all duration-300">
                  Learn More About Us
                </Button>
              </Link>
            </div>
            
            <div className="relative">
              <div className="relative w-full h-96 rounded-lg overflow-hidden">
                <div className="absolute inset-0 border-4 border-white rounded-lg shadow-2xl"></div>
                <div className="absolute inset-2 border-4 border-yellow-400 rounded-lg overflow-hidden">
                  <img 
                    src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&w=800&q=80" 
                    alt="Professional team at work" 
                    className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
              </div>
              {/* Decorative elements */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-yellow-400 rounded-full opacity-20"></div>
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-600 rounded-full opacity-20"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Features with Images */}
      <section className="py-16 bg-gray-50 relative">
        {/* Background Image Shapes */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-10 right-10 w-32 h-32 rounded-full overflow-hidden opacity-20">
            <img 
              src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?auto=format&fit=crop&w=400&q=80" 
              alt="Technology" 
              className="w-full h-full object-cover"
            />
          </div>
          <div className="absolute bottom-20 left-10 w-40 h-40 rounded-3xl overflow-hidden opacity-20 rotate-12">
            <img 
              src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&w=400&q=80" 
              alt="Business woman" 
              className="w-full h-full object-cover"
            />
          </div>
        </div>

        <div className="relative container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 animate-fade-in">
              Why Choose FundIt?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're transforming financial services in Zambia with innovative solutions, 
              exceptional service, and a commitment to your success.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardHeader>
                <div className="relative w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 overflow-hidden group-hover:bg-blue-200 transition-colors duration-300">
                  <div className="absolute inset-0 rounded-full overflow-hidden opacity-20">
                    <img 
                      src="https://images.unsplash.com/photo-1649972904349-6e44c42644a7?auto=format&fit=crop&w=400&q=80" 
                      alt="Fast processing" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <Clock className="relative text-blue-600 z-10" size={32} />
                </div>
                <CardTitle className="text-xl text-gray-900">Fast Processing</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Get your loan approved and disbursed within 24 hours. 
                  Our streamlined process eliminates lengthy waiting periods.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardHeader>
                <div className="relative w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 overflow-hidden group-hover:bg-green-200 transition-colors duration-300">
                  <div className="absolute inset-0 rounded-full overflow-hidden opacity-20">
                    <img 
                      src="https://images.unsplash.com/photo-1518495973542-4542c06a5843?auto=format&fit=crop&w=400&q=80" 
                      alt="No collateral" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <Shield className="relative text-green-600 z-10" size={32} />
                </div>
                <CardTitle className="text-xl text-gray-900">No Collateral</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Most of our loans don't require collateral, making financing 
                  accessible to more individuals and businesses.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group">
              <CardHeader>
                <div className="relative w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4 overflow-hidden group-hover:bg-yellow-200 transition-colors duration-300">
                  <div className="absolute inset-0 rounded-full overflow-hidden opacity-20">
                    <img 
                      src="https://images.unsplash.com/photo-1500673922987-e212871fec22?auto=format&fit=crop&w=400&q=80" 
                      alt="Flexible terms" 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <DollarSign className="relative text-yellow-600 z-10" size={32} />
                </div>
                <CardTitle className="text-xl text-gray-900">Flexible Terms</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600">
                  Tailored loan products with flexible repayment terms 
                  designed to suit your unique financial needs.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Services Overview with Artistic Image Shapes */}
      <section className="py-16 bg-white relative overflow-hidden">
        {/* Artistic Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-0 w-64 h-64 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full -translate-x-1/2 opacity-30"></div>
          <div className="absolute bottom-20 right-0 w-80 h-80 bg-gradient-to-r from-green-100 to-blue-100 rounded-full translate-x-1/3 opacity-30"></div>
        </div>

        <div className="relative container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Financial Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive financial products designed for individuals and businesses in Zambia.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="border-l-4 border-l-blue-600 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
              {/* Artistic shape with image */}
              <div className="absolute top-4 right-4 w-24 h-24 rounded-full overflow-hidden opacity-20 rotate-12">
                <img 
                  src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80" 
                  alt="Business growth" 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle className="text-2xl text-blue-900 flex items-center space-x-2">
                  <CreditCard className="text-blue-600" size={28} />
                  <span>Business Loans</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={16} />
                  <div>
                    <h4 className="font-semibold text-gray-900">Invoice Discounting</h4>
                    <p className="text-gray-600">Immediate working capital against your sales invoices</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={16} />
                  <div>
                    <h4 className="font-semibold text-gray-900">Order Financing</h4>
                    <p className="text-gray-600">Capital to pay suppliers upfront for verified purchase orders</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={16} />
                  <div>
                    <h4 className="font-semibold text-gray-900">Term Loans</h4>
                    <p className="text-gray-600">Flexible repayment tenures up to 12 months</p>
                  </div>
                </div>
                <p className="text-sm text-blue-600 font-medium bg-blue-50 p-2 rounded">
                  Amounts: K25,000 - K1,000,000
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-green-600 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
              {/* Artistic shape with image */}
              <div className="absolute top-4 right-4 w-24 h-24 rounded-3xl overflow-hidden opacity-20 -rotate-12">
                <img 
                  src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?auto=format&fit=crop&w=400&q=80" 
                  alt="Personal finance" 
                  className="w-full h-full object-cover"
                />
              </div>
              <CardHeader>
                <CardTitle className="text-2xl text-green-900 flex items-center space-x-2">
                  <Users className="text-green-600" size={28} />
                  <span>Personal Loans</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={16} />
                  <div>
                    <h4 className="font-semibold text-gray-900">Payroll-Deducted Loans</h4>
                    <p className="text-gray-600">Short to medium-term credit for employees with MOU companies</p>
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="text-green-500 mt-1 flex-shrink-0" size={16} />
                  <div>
                    <h4 className="font-semibold text-gray-900">Instant Salary Advances</h4>
                    <p className="text-gray-600">Quick funds for formal employees within 24 hours</p>
                  </div>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <p className="text-sm text-green-700">
                    <strong>Benefits:</strong> No collateral required, early repayment options, 
                    minimal documentation
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="text-center mt-8">
            <Link to="/services">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                View All Services
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section with Background Image */}
      <section className="py-16 bg-blue-600 text-white relative overflow-hidden">
        {/* Background Image with Overlay */}
        <div className="absolute inset-0">
          <img 
            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&w=1920&q=80" 
            alt="Success background" 
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-blue-600/80"></div>
        </div>
        
        <div className="relative container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied clients who have achieved their financial goals with FundIt. 
            Apply today and get funded within 24 hours.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/application">
              <Button size="lg" className="bg-yellow-500 hover:bg-yellow-600 text-black font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
                Apply Now
              </Button>
            </Link>
            <Link to="/contact">
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600 transition-all duration-300">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Index;
