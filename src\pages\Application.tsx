
import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';

const Application = () => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    nrcNumber: '',
    dateOfBirth: '',
    maritalStatus: '',
    address: '',
    city: '',
    
    // Employment Information
    employmentStatus: '',
    employerName: '',
    jobTitle: '',
    monthlyIncome: '',
    employmentDuration: '',
    
    // Loan Information
    loanType: '',
    loanAmount: '',
    loanPurpose: '',
    repaymentPeriod: '',
    
    // Additional Information
    hasExistingLoans: false,
    existingLoansDetails: '',
    bankName: '',
    accountNumber: '',
    
    // Agreements
    agreeToTerms: false,
    agreeToCredit: false
  });

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agreeToTerms || !formData.agreeToCredit) {
      toast({
        title: "Agreement Required",
        description: "Please agree to the terms and conditions and credit check authorization.",
        variant: "destructive"
      });
      return;
    }

    console.log('Form submitted:', formData);
    toast({
      title: "Application Submitted Successfully!",
      description: "We'll review your application and contact you within 24 hours.",
    });
    
    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      nrcNumber: '',
      dateOfBirth: '',
      maritalStatus: '',
      address: '',
      city: '',
      employmentStatus: '',
      employerName: '',
      jobTitle: '',
      monthlyIncome: '',
      employmentDuration: '',
      loanType: '',
      loanAmount: '',
      loanPurpose: '',
      repaymentPeriod: '',
      hasExistingLoans: false,
      existingLoansDetails: '',
      bankName: '',
      accountNumber: '',
      agreeToTerms: false,
      agreeToCredit: false
    });
    setCurrentStep(1);
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Personal Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="firstName">First Name *</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) => updateFormData('firstName', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="lastName">Last Name *</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) => updateFormData('lastName', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => updateFormData('email', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone Number *</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => updateFormData('phone', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="nrcNumber">NRC Number *</Label>
          <Input
            id="nrcNumber"
            value={formData.nrcNumber}
            onChange={(e) => updateFormData('nrcNumber', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="dateOfBirth">Date of Birth *</Label>
          <Input
            id="dateOfBirth"
            type="date"
            value={formData.dateOfBirth}
            onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
            required
          />
        </div>
      </div>
      
      <div>
        <Label>Marital Status *</Label>
        <RadioGroup
          value={formData.maritalStatus}
          onValueChange={(value) => updateFormData('maritalStatus', value)}
          className="mt-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="single" id="single" />
            <Label htmlFor="single">Single</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="married" id="married" />
            <Label htmlFor="married">Married</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="divorced" id="divorced" />
            <Label htmlFor="divorced">Divorced</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="widowed" id="widowed" />
            <Label htmlFor="widowed">Widowed</Label>
          </div>
        </RadioGroup>
      </div>

      <div>
        <Label htmlFor="address">Residential Address *</Label>
        <Textarea
          id="address"
          value={formData.address}
          onChange={(e) => updateFormData('address', e.target.value)}
          required
        />
      </div>

      <div>
        <Label htmlFor="city">City *</Label>
        <Input
          id="city"
          value={formData.city}
          onChange={(e) => updateFormData('city', e.target.value)}
          required
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Employment Information</h3>
      
      <div>
        <Label>Employment Status *</Label>
        <RadioGroup
          value={formData.employmentStatus}
          onValueChange={(value) => updateFormData('employmentStatus', value)}
          className="mt-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="employed" id="employed" />
            <Label htmlFor="employed">Employed (Permanent)</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="contract" id="contract" />
            <Label htmlFor="contract">Contract Employee</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="selfemployed" id="selfemployed" />
            <Label htmlFor="selfemployed">Self Employed</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="business" id="business" />
            <Label htmlFor="business">Business Owner</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="employerName">Employer/Company Name *</Label>
          <Input
            id="employerName"
            value={formData.employerName}
            onChange={(e) => updateFormData('employerName', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="jobTitle">Job Title/Position *</Label>
          <Input
            id="jobTitle"
            value={formData.jobTitle}
            onChange={(e) => updateFormData('jobTitle', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="monthlyIncome">Monthly Income (ZMW) *</Label>
          <Input
            id="monthlyIncome"
            type="number"
            value={formData.monthlyIncome}
            onChange={(e) => updateFormData('monthlyIncome', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="employmentDuration">Employment Duration (Years) *</Label>
          <Input
            id="employmentDuration"
            type="number"
            step="0.1"
            value={formData.employmentDuration}
            onChange={(e) => updateFormData('employmentDuration', e.target.value)}
            required
          />
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Loan Information</h3>
      
      <div>
        <Label>Loan Type *</Label>
        <RadioGroup
          value={formData.loanType}
          onValueChange={(value) => updateFormData('loanType', value)}
          className="mt-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="personal" id="personal" />
            <Label htmlFor="personal">Personal Loan</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="payroll" id="payroll" />
            <Label htmlFor="payroll">Payroll-Deducted Loan</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="salary" id="salary" />
            <Label htmlFor="salary">Instant Salary Advance</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="business" id="businessloan" />
            <Label htmlFor="businessloan">Business Loan</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="invoice" id="invoice" />
            <Label htmlFor="invoice">Invoice Discounting</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="order" id="order" />
            <Label htmlFor="order">Order Financing</Label>
          </div>
        </RadioGroup>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="loanAmount">Loan Amount (ZMW) *</Label>
          <Input
            id="loanAmount"
            type="number"
            value={formData.loanAmount}
            onChange={(e) => updateFormData('loanAmount', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="repaymentPeriod">Preferred Repayment Period (Months) *</Label>
          <select
            id="repaymentPeriod"
            value={formData.repaymentPeriod}
            onChange={(e) => updateFormData('repaymentPeriod', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          >
            <option value="">Select Period</option>
            <option value="1">1 Month</option>
            <option value="3">3 Months</option>
            <option value="6">6 Months</option>
            <option value="12">12 Months</option>
          </select>
        </div>
      </div>

      <div>
        <Label htmlFor="loanPurpose">Loan Purpose *</Label>
        <Textarea
          id="loanPurpose"
          value={formData.loanPurpose}
          onChange={(e) => updateFormData('loanPurpose', e.target.value)}
          placeholder="Please describe how you plan to use the loan"
          required
        />
      </div>
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-semibold text-gray-900">Additional Information & Banking Details</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="bankName">Bank Name *</Label>
          <Input
            id="bankName"
            value={formData.bankName}
            onChange={(e) => updateFormData('bankName', e.target.value)}
            required
          />
        </div>
        <div>
          <Label htmlFor="accountNumber">Account Number *</Label>
          <Input
            id="accountNumber"
            value={formData.accountNumber}
            onChange={(e) => updateFormData('accountNumber', e.target.value)}
            required
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="hasExistingLoans"
            checked={formData.hasExistingLoans}
            onCheckedChange={(checked) => updateFormData('hasExistingLoans', checked)}
          />
          <Label htmlFor="hasExistingLoans">I have existing loans</Label>
        </div>

        {formData.hasExistingLoans && (
          <div>
            <Label htmlFor="existingLoansDetails">Existing Loans Details</Label>
            <Textarea
              id="existingLoansDetails"
              value={formData.existingLoansDetails}
              onChange={(e) => updateFormData('existingLoansDetails', e.target.value)}
              placeholder="Please provide details about your existing loans (lender, amount, monthly payment)"
            />
          </div>
        )}
      </div>

      <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold text-gray-900">Agreements & Consent</h4>
        
        <div className="flex items-start space-x-2">
          <Checkbox
            id="agreeToTerms"
            checked={formData.agreeToTerms}
            onCheckedChange={(checked) => updateFormData('agreeToTerms', checked)}
          />
          <Label htmlFor="agreeToTerms" className="text-sm">
            I agree to the terms and conditions of FundIt Capital Solutions Limited. 
            I understand the loan terms, interest rates, and repayment obligations. *
          </Label>
        </div>

        <div className="flex items-start space-x-2">
          <Checkbox
            id="agreeToCredit"
            checked={formData.agreeToCredit}
            onCheckedChange={(checked) => updateFormData('agreeToCredit', checked)}
          />
          <Label htmlFor="agreeToCredit" className="text-sm">
            I authorize FundIt Capital Solutions Limited to conduct credit checks 
            and verify the information provided in this application. *
          </Label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-900 to-blue-700 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Loan Application</h1>
            <p className="text-xl text-blue-100">
              Complete your loan application in just a few minutes. Get approved within 24 hours.
            </p>
          </div>
        </div>
      </section>

      {/* Application Form */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Progress Indicator */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                {[1, 2, 3, 4].map((step) => (
                  <div key={step} className="flex items-center">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold ${
                        step <= currentStep
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step}
                    </div>
                    {step < 4 && (
                      <div
                        className={`h-1 w-16 mx-2 ${
                          step < currentStep ? 'bg-blue-600' : 'bg-gray-300'
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>
              <div className="flex justify-between mt-2 text-sm text-gray-600">
                <span>Personal Info</span>
                <span>Employment</span>
                <span>Loan Details</span>
                <span>Final Steps</span>
              </div>
            </div>

            <Card className="shadow-lg">
              <CardHeader>
                <CardTitle className="text-2xl text-blue-900">
                  Step {currentStep} of 4
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit}>
                  {currentStep === 1 && renderStep1()}
                  {currentStep === 2 && renderStep2()}
                  {currentStep === 3 && renderStep3()}
                  {currentStep === 4 && renderStep4()}

                  <div className="flex justify-between mt-8">
                    {currentStep > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={prevStep}
                      >
                        Previous
                      </Button>
                    )}
                    
                    {currentStep < 4 ? (
                      <Button
                        type="button"
                        onClick={nextStep}
                        className="bg-blue-600 hover:bg-blue-700 ml-auto"
                      >
                        Next
                      </Button>
                    ) : (
                      <Button
                        type="submit"
                        className="bg-green-600 hover:bg-green-700 ml-auto"
                      >
                        Submit Application
                      </Button>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Application;
